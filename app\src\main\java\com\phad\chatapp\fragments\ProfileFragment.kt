package com.phad.chatapp.fragments

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.platform.ComposeView
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.FieldValue
import com.phad.chatapp.LoginActivity
import com.phad.chatapp.R
import com.phad.chatapp.ui.profile.ProfileScreen
import com.phad.chatapp.ui.profile.ProfileUiState
import com.phad.chatapp.utils.SessionManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await

class ProfileFragment : Fragment() {
    private val TAG = "ProfileFragment"
    private lateinit var sessionManager: SessionManager
    
    private val _uiState = MutableStateFlow(ProfileUiState())
    private val uiState: StateFlow<ProfileUiState> = _uiState.asStateFlow()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setContent {
                val state by uiState.collectAsState()
                ProfileScreen(
                    state = state,
                    onLogoutClick = { logout() },
                    onChatbotClick = {
                        val intent = Intent(requireContext(), com.phad.chatapp.features.home.chatbot.ui.ChatBotActivity::class.java)
                        startActivity(intent)
                    },
                    onLibraryClick = {
                        findNavController().navigate(R.id.action_profileFragment_to_libraryItemListFragment)
                    },
                    onChatClick = {},
                    onScheduleClick = {},
                    onSwitchInterfaceClick = {
                        // Only launch if eligible
                        if (!state.isStudent && state.Teaching_wing) {
                            val intent = Intent(requireContext(), com.phad.chatapp.ui.profile.NssOrTeachingWingActivity::class.java)
                            intent.putExtra("teaching_wing", true) // Ensure the choice screen appears
                            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                            startActivity(intent)
                        }
                    },
                    currentInterface = "Teaching Wing"
                )
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        // Initialize session manager
        sessionManager = SessionManager(requireContext())
        
        // Load user profile from session
        loadProfileFromSession()
    }
    
    private fun logout() {
        Log.d(TAG, "Logout button clicked")
        try {
            // Clear session data
                sessionManager.logoutUser()

            // Sign out from Firebase
            FirebaseAuth.getInstance().signOut()

            // Navigate to LoginActivity
            val intent = Intent(requireContext(), LoginActivity::class.java)
                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                startActivity(intent)
            requireActivity().finish()
            } catch (e: Exception) {
                Log.e(TAG, "Error during logout: ${e.message}", e)
                Toast.makeText(requireContext(), "Logout failed: ${e.message}", Toast.LENGTH_LONG).show()
            }
        }
        
    // Call this after login, after marking attendance (students), or from chat tab refresh (admins)
    fun refreshAttendanceStats() {
        val userType = sessionManager.fetchUserType()
        val rollNumber = sessionManager.fetchUserId()
        val db = FirebaseFirestore.getInstance()
        val metaRef = db.collection("meta").document("statistics")
        if (userType == "Student") {
            val studentRef = db.collection("Student").document(rollNumber)
            lifecycleScope.launch {
                try {
                    val studentDoc = studentRef.get().await()
                    val attended = studentDoc.getLong("event_attendance") ?: 0
                    val metaDoc = metaRef.get().await()
                    val total = metaDoc.getLong("total_events") ?: 0
                    val stats = "$attended/$total"
                    _uiState.update { it.copy(events = stats) }
                    sessionManager.saveAttendanceStats(stats)
                } catch (e: Exception) {
                    _uiState.update { it.copy(events = "0/0") }
                    sessionManager.saveAttendanceStats("0/0")
                    Log.e(TAG, "Error refreshing attendance statistics", e)
                }
            }
        } else { // Admin or other
            lifecycleScope.launch {
                try {
                    val metaDoc = metaRef.get().await()
                    val total = metaDoc.getLong("total_events") ?: 0
                    val stats = "-/$total"
                    _uiState.update { it.copy(events = stats) }
                    sessionManager.saveAttendanceStats(stats)
                } catch (e: Exception) {
                    _uiState.update { it.copy(events = "-/0") }
                    sessionManager.saveAttendanceStats("-/0")
                    Log.e(TAG, "Error refreshing admin event statistics", e)
                }
            }
        }
    }

    private fun loadProfileFromSession() {
        val profile = sessionManager.getProfileFromSession()
        // Use attendance stats from session for UI
        val attendanceStats = sessionManager.fetchAttendanceStats()
        _uiState.value = profile.copy(events = attendanceStats)
        // Do NOT call any Firestore reads here for profile fields
        // If you want to update attendance/class/meeting stats, do so only on login or explicit update
    }
    
    private fun loadStatistics(rollNumber: String?) {
        val userType = sessionManager.fetchUserType()
        if (userType != "Student") {
            _uiState.update { it.copy(events = "-/-") }
            return
        }
        if (rollNumber == null) {
            Log.e(TAG, "Cannot load statistics: Roll number is null")
            return
        }
        val db = FirebaseFirestore.getInstance()
        val studentRef = db.collection("Student").document(rollNumber)
        val metaRef = db.collection("meta").document("statistics")
        lifecycleScope.launch {
            try {
                val studentDoc = studentRef.get().await()
                val attended = studentDoc.getLong("event_attendance") ?: 0
                val metaDoc = metaRef.get().await()
                val total = metaDoc.getLong("total_events") ?: 0
                _uiState.update { it.copy(events = "$attended/$total") }
            } catch (e: Exception) {
                _uiState.update { it.copy(events = "0/0") }
                Log.e(TAG, "Error loading attendance statistics", e)
            }
        }

        // Load classes statistics
        db.collection("classes")
            .whereEqualTo("userId", rollNumber)
            .get()
            .addOnSuccessListener { documents ->
                val totalClasses = documents.size()
                val completedClasses = documents.count { it.getString("status") == "COMPLETED" }
                _uiState.update { it.copy(classes = "$completedClasses/$totalClasses") }
                Log.d(TAG, "Loaded classes statistics: $completedClasses/$totalClasses")
            }
            .addOnFailureListener {
                _uiState.update { it.copy(classes = "0/0") }
                Log.e(TAG, "Error loading classes statistics")
            }
        
        // Load meetings statistics
        db.collection("meetings")
            .whereEqualTo("rollNumber", rollNumber)
            .get()
            .addOnSuccessListener { documents ->
                val totalMeetings = documents.size()
                val attendedMeetings = documents.count { it.getBoolean("attended") == true }
                _uiState.update { it.copy(meetings = "$attendedMeetings/$totalMeetings") }
                Log.d(TAG, "Loaded meetings statistics: $attendedMeetings/$totalMeetings")
            }
            .addOnFailureListener {
                _uiState.update { it.copy(meetings = "0/0") }
                Log.e(TAG, "Error loading meetings statistics")
            }
    }
} 