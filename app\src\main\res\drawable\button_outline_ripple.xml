<?xml version="1.0" encoding="utf-8"?>
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
    android:color="#33FFFFFF">
    <item>
        <selector>
            <item android:state_pressed="true">
                <shape android:shape="rectangle">
                    <solid android:color="#D9D9D9" />
                    <corners android:radius="8dp" />
                </shape>
            </item>
            <item>
                <shape android:shape="rectangle">
                    <solid android:color="@android:color/transparent" />
                    <stroke android:color="#D9D9D9" android:width="3dp" />
                    <corners android:radius="8dp" />
                </shape>
            </item>
        </selector>
    </item>
</ripple> 