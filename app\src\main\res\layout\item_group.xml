<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="16dp"
    android:background="?attr/selectableItemBackground">

    <ImageView
        android:id="@+id/image_group"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:src="@android:drawable/ic_menu_myplaces"
        android:background="@color/ui_blue"
        android:padding="8dp"
        android:contentDescription="Group" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical"
        android:layout_marginStart="16dp">

        <TextView
            android:id="@+id/text_group_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Group Name"
            android:textColor="#333333"
            android:textSize="16sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/text_group_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Group description"
            android:textColor="#777777"
            android:textSize="14sp"
            android:layout_marginTop="4dp"
            android:maxLines="2"
            android:ellipsize="end" />
    </LinearLayout>

</LinearLayout> 