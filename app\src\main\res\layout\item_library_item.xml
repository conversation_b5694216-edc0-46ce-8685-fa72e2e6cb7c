<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="16dp"
    android:background="?android:attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true"
    android:layout_marginBottom="8dp">

    <ImageView
        android:id="@+id/imageViewItemIcon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_gravity="center_vertical"
        android:layout_marginEnd="16dp"
        android:contentDescription="@string/item_icon" />

    <TextView
        android:id="@+id/textViewItemTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:layout_gravity="center_vertical"
        android:textAppearance="@style/TextAppearance.AppCompat.Medium"
        android:textSize="18sp"
        android:textColor="#000000"
        tools:text="Item Title" />

    <TextView
        android:id="@+id/textViewFileLink"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:textSize="14sp"
        android:textColor="#000000"
        android:autoLink="web"
        android:paddingTop="4dp"
        android:visibility="gone"
        tools:text="https://drive.google.com/..."
        tools:visibility="visible" />

</LinearLayout> 