package com.phad.chatapp

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.FrameLayout
import android.widget.Toast
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.updatePadding
import androidx.fragment.app.Fragment
import androidx.navigation.NavController
import androidx.navigation.fragment.NavHostFragment
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.FirebaseFirestoreException
import com.phad.chatapp.utils.FirestoreSetup
import com.phad.chatapp.utils.NetworkUtils
import com.phad.chatapp.utils.SessionManager
import com.phad.chatapp.utils.NotificationHelper
import com.phad.chatapp.utils.MultiDatabaseHelper
import com.phad.chatapp.fragments.HomeFragment
import com.phad.chatapp.features.calendar.ui.CalendarFragment
import com.phad.chatapp.fragments.ProfileFragment
import com.phad.chatapp.fragments.ScheduleFragment
import com.phad.chatapp.features.scheduling.SchedulingFragment
import android.widget.ImageButton
import com.phad.chatapp.fragments.AdminAttendanceApprovalFragment
import com.phad.chatapp.fragments.AttendanceManagerFragment

class MainActivity : AppCompatActivity() {
    private val TAG = "MainActivity"
    private lateinit var sessionManager: SessionManager
    private lateinit var auth: FirebaseAuth
    private lateinit var notificationHelper: NotificationHelper
    private lateinit var navController: NavController

    // User data
    private var userType: String? = null
    private var userRoll: String? = null
    private var userYear: Int = 0
    
    // Request code for notification permission
    private val NOTIFICATION_PERMISSION_REQUEST_CODE = 100

    override fun onCreate(savedInstanceState: Bundle?) {
        Log.d("MainActivity", "onCreate start")
        super.onCreate(savedInstanceState)
        Log.d("MainActivity", "after super.onCreate")

        // Removed enable edge to edge display - handling insets manually
        
        // Initialize Firebase Auth
        auth = FirebaseAuth.getInstance()
        
        // Initialize session manager
        sessionManager = SessionManager(this)
        
        // Initialize notification helper
        notificationHelper = NotificationHelper(this)
        
        // Request notification permission if needed
        requestNotificationPermissionIfNeeded()
        
        // Set up window flags for proper status bar handling
        WindowCompat.setDecorFitsSystemWindows(window, false)
        
        // Set status bar color to transparent to allow content to draw behind it
        window.statusBarColor = getColor(android.R.color.transparent)
        
        // Make status bar icons light for better visibility on dark background
        WindowCompat.getInsetsController(window, window.decorView).apply {
            isAppearanceLightStatusBars = false
        }
        
        setContentView(R.layout.activity_main)
        Log.d("MainActivity", "after setContentView")

        // Set up NavController
        val navHostFragment = supportFragmentManager
            .findFragmentById(R.id.nav_host_fragment) as NavHostFragment
        navController = navHostFragment.navController

        // Removed action bar setup - toolbar is handled by fragments now
        
        // Set up window insets for edge-to-edge
        setupWindowInsets()
        Log.d("MainActivity", "after setupWindowInsets")

        // Load user data - will also set up navigation
        loadUserData()
        Log.d("MainActivity", "after loadUserData")

        // Start notification listener for the current user
        val currentUserId = sessionManager.fetchUserId()
        if (currentUserId.isNotEmpty()) {
            notificationHelper.startListeningForNotifications(currentUserId)
        }
        Log.d("MainActivity", "after notificationHelper.startListeningForNotifications")

        // Set up custom navigation buttons
        setupCustomNavigation()
        Log.d("MainActivity", "after setupCustomNavigation")
    }
    
    override fun onResume() {
        Log.d("MainActivity", "onResume start")
        super.onResume()
        Log.d("MainActivity", "after super.onResume")

        // Restart notification listener when returning to the app
        val currentUserId = sessionManager.fetchUserId()
        if (currentUserId.isNotEmpty()) {
            notificationHelper.startListeningForNotifications(currentUserId)
        }
        Log.d("MainActivity", "onResume end")
    }
    
    override fun onPause() {
        super.onPause()
        
        // Stop notification listener when app is in background
        notificationHelper.stopListeningForNotifications()
    }
    
    override fun onDestroy() {
        super.onDestroy()
        
        // Ensure notification listener is stopped to prevent memory leaks
        notificationHelper.stopListeningForNotifications()
    }

    /**
     * Request notification permission for Android 13+
     */
    private fun requestNotificationPermissionIfNeeded() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(
                    this,
                    Manifest.permission.POST_NOTIFICATIONS
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                ActivityCompat.requestPermissions(
                    this,
                    arrayOf(Manifest.permission.POST_NOTIFICATIONS),
                    NOTIFICATION_PERMISSION_REQUEST_CODE
                )
            }
        }
    }
    
    /**
     * Handle permission request results
     */
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        
        if (requestCode == NOTIFICATION_PERMISSION_REQUEST_CODE) {
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                Log.d(TAG, "Notification permission granted")
            } else {
                Log.w(TAG, "Notification permission denied - notifications won't work")
            }
        }
    }
    
    private fun redirectToLogin() {
        val intent = Intent(this, LoginActivity::class.java)
        startActivity(intent)
        finish()
    }
    
    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.main_menu, menu)
        
        // Get user type from session
        val userType = sessionManager.fetchUserType()
        
        // Show/hide admin menu items based on user type
        val isAdmin1 = userType == "Admin1"
        val isAdmin = isAdmin1 || userType == "Admin2"

        // Only Admin1 can create/remove groups
        menu.findItem(R.id.action_create_group)?.isVisible = isAdmin1
        menu.findItem(R.id.action_remove_group)?.isVisible = isAdmin1
        
        return true
    }
    
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_search -> {
                Toast.makeText(this, "Search not implemented yet", Toast.LENGTH_SHORT).show()
                true
            }
            R.id.action_create_group -> {
                showCreateGroupScreen()
                true
            }
            R.id.action_remove_group -> {
                showRemoveGroupScreen()
                true
            }
            R.id.action_logout -> {
                sessionManager.logoutUser()
                redirectToLogin()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
    
    private fun diagnoseAndFixFirebaseConfig() {
        Log.d(TAG, "Diagnosing Firebase configuration...")
        val (isFixed, message) = FirestoreSetup.diagnoseAndFixFirebaseConfig(this)
        
        if (!isFixed) {
            Log.e(TAG, "Firebase configuration issue: $message")
            Toast.makeText(
                this, 
                "Firebase configuration issue: $message", 
                Toast.LENGTH_LONG
            ).show()
        } else {
            Log.d(TAG, "Firebase configuration: $message")
        }
        
        // Also check network connectivity
        val hasNetwork = NetworkUtils.isNetworkAvailable(this)
        Log.d(TAG, "Network available: $hasNetwork")
        
        if (!hasNetwork) {
            Toast.makeText(
                this,
                "No internet connection. The app may not function properly.",
                Toast.LENGTH_LONG
            ).show()
        }
    }
    
    private fun checkAndSetupSampleData() {
        Log.d(TAG, "Checking if sample data needs to be setup")
        try {
            // Check Firestore and set up sample data if needed
            val firestore = FirebaseFirestore.getInstance()
            
            // Check if users collection exists and has data
            firestore.collection("users").limit(1).get()
                .addOnSuccessListener { snapshot ->
                    if (!snapshot.isEmpty) {
                        Log.d(TAG, "Users collection exists and has data")
                    } else {
                        Log.w(TAG, "Users collection is empty")
                    }
                }
                .addOnFailureListener { e ->
                    Log.e(TAG, "Failed to access Firestore", e)
                    
                    // If we can't access Firestore, show a more helpful message
                    val errorMessage = if (e is FirebaseFirestoreException && e.code == FirebaseFirestoreException.Code.UNAVAILABLE) {
                        "Firestore is unavailable. Check your internet connection."
                    } else {
                        "Firestore access error: ${e.message}"
                    }
                    
                    Toast.makeText(this@MainActivity, errorMessage, Toast.LENGTH_LONG).show()
                }
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to check sample data", e)
            Toast.makeText(this, "Error: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }
    
    private fun setupNavigation() {
        // Initialize with HomeFragment
        if (supportFragmentManager.findFragmentById(R.id.nav_host_fragment) == null) {
            navController.navigate(R.id.homeFragment)
        }
    }
    
    /**
     * Updates the bottom navigation appearance based on the background color of the current screen
     * @param isWhiteBackground true if the background is white, false if it's dark
     */
    private fun updateBottomNavAppearance(isWhiteBackground: Boolean) {
        val bottomNavContainer = findViewById<FrameLayout>(R.id.bottom_nav_container)
        
        if (isWhiteBackground) {
            // For white backgrounds, make the nav bar more visible with shadow and elevation
            val whiteElevation = resources.getDimension(R.dimen.nav_elevation_white_bg)
            bottomNavContainer.elevation = whiteElevation
        } else {
            // For dark backgrounds, keep it more subtle
            val darkElevation = resources.getDimension(R.dimen.nav_elevation_dark_bg)
            bottomNavContainer.elevation = darkElevation
        }
    }

    private fun showCreateGroupScreen() {
        navController.navigate(R.id.addGroupFragment)
    }

    private fun showRemoveGroupScreen() {
        navController.navigate(R.id.removeGroupFragment)
    }

    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }

    private fun setupWindowInsets() {
        val mainLayout = findViewById<View>(R.id.main)
        mainLayout?.let {
            ViewCompat.setOnApplyWindowInsetsListener(it) { v, insets ->
                val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
                v.updatePadding(
                    left = systemBars.left,
                    top = systemBars.top,
                    right = systemBars.right,
                    bottom = systemBars.bottom
                )
                insets
            }
        }

        // Removed obsolete toolbar content handling.
    }

    private fun loadUserData() {
        // Check if user is logged in
        if (!sessionManager.isLoggedIn()) {
            // Not logged in, redirect to login
            redirectToLogin()
            return
        } else {
            // Get user data from session
            val userData = sessionManager.getUserDetails()
            userType = userData[SessionManager.KEY_USER_TYPE] as String?
            userRoll = userData[SessionManager.KEY_USER_ROLL_NUMBER] as String?
            userYear = userData[SessionManager.KEY_USER_YEAR] as Int? ?: 0
            
            // Set up the UI immediately to show home screen first
            setupNavigation()
            
            // Log user details to help with debugging
            Log.d(TAG, "User data from session: type=$userType, roll=$userRoll, year=$userYear")
            
            // Additional checks for Firebase user
            val firebaseUser = auth.currentUser
            if (firebaseUser == null) {
                // Firebase Auth session expired, redirect to login
                Log.d(TAG, "Firebase user is null, redirecting to login")
                sessionManager.logoutUser()
                redirectToLogin()
                return
            } else {
                // Log Firebase user details for debugging
                Log.d(TAG, "Firebase user: uid=${firebaseUser.uid}, email=${firebaseUser.email}, displayName=${firebaseUser.displayName}")
                
                // If displayName contains userType and roll number, parse it
                firebaseUser.displayName?.let { displayName ->
                    if (displayName.contains(":")) {
                        val parts = displayName.split(":")
                        if (parts.size >= 2) {
                            val typeFromDisplay = parts[0]
                            val rollFromDisplay = parts[1]
                            
                            Log.d(TAG, "Firebase displayName parsed: type=$typeFromDisplay, roll=$rollFromDisplay")
                            
                            // If userType is null, use the one from displayName
                            if (userType.isNullOrEmpty()) {
                                userType = typeFromDisplay
                                Log.d(TAG, "Using userType from Firebase displayName: $userType")
                            }
                        }
                    }
                }
            }
        }
        
        // Firebase is already initialized in ChatApplication
        // Now just check if sample data needs to be set up
        checkAndSetupSampleData()
    }

    /**
     * Test connection to the secondary Firebase database
     * This is just for testing and can be removed in production
     */
    private fun testSecondaryFirebase() {
        val secondaryDb = MultiDatabaseHelper.getSecondaryFirestore()
        
        // Test write operation
        secondaryDb.collection("test")
            .document("test_document")
            .set(mapOf(
                "timestamp" to com.google.firebase.Timestamp.now(),
                "message" to "Test from ChatApp"
            ))
            .addOnSuccessListener {
                Log.d(TAG, "Successfully wrote to secondary Firebase")
                Toast.makeText(this, "Secondary Firebase connection successful", Toast.LENGTH_SHORT).show()
            }
            .addOnFailureListener { e ->
                Log.e(TAG, "Failed to write to secondary Firebase", e)
                Toast.makeText(this, "Secondary Firebase connection failed", Toast.LENGTH_SHORT).show()
            }
    }

    // Method to open the Calendar module
    fun openCalendar() {
        try {
            // Try to load the CalendarFragment from the calendar module
            val calendarFragmentClass = Class.forName("com.phad.chatapp.features.calendar.ui.CalendarFragment")
            val calendarFragment = calendarFragmentClass.getMethod("newInstance").invoke(null) as Fragment
            
            // Replace the current fragment with the calendar fragment
            navController.navigate(R.id.calendarFragment)
        } catch (e: Exception) {
            // If calendar module is not available, show a toast
            Toast.makeText(this, "Calendar feature is not available", Toast.LENGTH_SHORT).show()
            Log.e(TAG, "Error opening calendar: ${e.message}")
        }
    }

    /**
     * Set up custom navigation buttons
     */
    private fun setupCustomNavigation() {
        // Find all navigation buttons
        val btnHome = findViewById<ImageButton>(R.id.btn_home)
        val btnChat = findViewById<ImageButton>(R.id.btn_chat)
        val btnCalendar = findViewById<ImageButton>(R.id.btn_calendar)
        val btnSchedule = findViewById<ImageButton>(R.id.btn_schedule)
        val btnProfile = findViewById<ImageButton>(R.id.btn_profile)
        val btnInterview = findViewById<ImageButton>(R.id.btn_interview)

        // Get user type from session
        val isStudent = sessionManager.fetchIsStudent()
        
        // Show/hide scheduling button based on user type
        if (!isStudent) {
            // For admins: show scheduling button and interview button
            btnSchedule.visibility = View.VISIBLE
            btnInterview.visibility = View.VISIBLE
        } else {
            // For regular users: hide scheduling button and interview button
            btnSchedule.visibility = View.GONE
            btnInterview.visibility = View.GONE
        }
        
        // Set initial selection
        btnHome.setColorFilter(ContextCompat.getColor(this, R.color.blue))
        
        // Set click listeners
        btnHome.setOnClickListener {
            navController.navigate(R.id.homeFragment)
            resetNavButtonColors()
            btnHome.setColorFilter(ContextCompat.getColor(this, R.color.blue))
        }
        
        btnChat.setOnClickListener {
            navController.navigate(R.id.chatFragment)
            resetNavButtonColors()
            btnChat.setColorFilter(ContextCompat.getColor(this, R.color.blue))
        }
        
        btnCalendar.setOnClickListener {
            navController.navigate(R.id.calendarFragment)
            resetNavButtonColors()
            btnCalendar.setColorFilter(ContextCompat.getColor(this, R.color.blue))
        }
        
        btnSchedule.setOnClickListener {
            navController.navigate(R.id.schedulingFragment)
            resetNavButtonColors()
            btnSchedule.setColorFilter(ContextCompat.getColor(this, R.color.blue))
        }
        
        btnProfile.setOnClickListener {
            navController.navigate(R.id.profileFragment)
            resetNavButtonColors()
            btnProfile.setColorFilter(ContextCompat.getColor(this, R.color.blue))
        }

        btnInterview.setOnClickListener {
            navController.navigate(R.id.interviewFragment)
            resetNavButtonColors()
            btnInterview.setColorFilter(ContextCompat.getColor(this, R.color.blue))
        }
    }
    
    /**
     * Reset all nav button colors to white
     */
    private fun resetNavButtonColors() {
        findViewById<ImageButton>(R.id.btn_home).clearColorFilter()
        findViewById<ImageButton>(R.id.btn_chat).clearColorFilter()
        findViewById<ImageButton>(R.id.btn_calendar).clearColorFilter()
        findViewById<ImageButton>(R.id.btn_schedule).clearColorFilter()
        findViewById<ImageButton>(R.id.btn_profile).clearColorFilter()
        findViewById<ImageButton>(R.id.btn_interview).clearColorFilter()
    }

    /**
     * Load a fragment into the content frame
     */
    fun navigateToAttendanceManager() {
        navController.navigate(R.id.attendanceManagerFragment)
    }

    /**
     * Navigate to the Admin Attendance Approval screen
     */
    fun navigateToAttendanceApproval() {
        navController.navigate(R.id.adminAttendanceApprovalFragment)
    }
}