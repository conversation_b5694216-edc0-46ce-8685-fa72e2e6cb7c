<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#0D0302"
    tools:context=".NssMainActivity">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/ui_dark"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.fragment.app.FragmentContainerView
            android:id="@+id/nav_host_fragment"
            android:name="androidx.navigation.fragment.NavHostFragment"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="#0D0302"
            app:defaultNavHost="true"
            app:navGraph="@navigation/nav_graph_nss"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Floating bottom navigation container - truly floating above content -->
    <FrameLayout
        android:id="@+id/bottom_nav_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        android:elevation="12dp"
        android:translationZ="12dp"
        android:background="@android:color/transparent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <!-- Custom navigation bar with simple buttons -->
        <LinearLayout
            android:layout_width="210dp"
            android:layout_height="56dp"
            android:orientation="horizontal"
            android:background="@drawable/bg_bottom_nav_transparent"
            android:gravity="center"
            android:paddingHorizontal="8dp">

            <ImageButton
                android:id="@+id/btn_home"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:src="@drawable/ic_home"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="Home" />

            <ImageButton
                android:id="@+id/btn_calendar"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:src="@drawable/ic_calendar"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="Calendar" />

            <ImageButton
                android:id="@+id/btn_profile"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:src="@drawable/ic_profile"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="Profile" />

        </LinearLayout>

    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout> 