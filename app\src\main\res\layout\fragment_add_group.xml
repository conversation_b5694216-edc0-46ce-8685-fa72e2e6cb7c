<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FFFFFF"
    android:fitsSystemWindows="true">

    <!-- Status bar spacer -->
    <View
        android:id="@+id/status_bar_spacer"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintHeight_percent="0.04"/>

    <!-- Scrollable content container -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        android:scrollbars="none"
        android:clipToPadding="false"
        android:paddingTop="8dp"
        app:layout_constraintTop_toBottomOf="@id/status_bar_spacer"
        app:layout_constraintBottom_toTopOf="@id/create_group_button">

        <!-- Content container - now inside NestedScrollView -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Card View for Group Creation Form -->
            <androidx.cardview.widget.CardView
                android:id="@+id/form_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="8dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="8dp"
                app:cardBackgroundColor="#FFFFFF">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <!-- Custom toolbar with back button and title -->
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/toolbar_container"
                        android:layout_width="match_parent"
                        android:layout_height="?attr/actionBarSize"
                        android:background="#0D0302"
                        android:paddingStart="16dp"
                        android:paddingEnd="16dp"
                        android:importantForAccessibility="yes"
                        android:visibility="visible">

                        <ImageView
                            android:id="@+id/back_button"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_back_arrow"
                            android:contentDescription="@string/back_navigation"
                            android:clickable="true"
                            android:focusable="true"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintBottom_toBottomOf="parent" />

                        <TextView
                            android:id="@+id/toolbar_title"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Create Group"
                            android:textColor="#FFFFFF"
                            android:textSize="20sp"
                            android:layout_marginStart="12dp"
                            app:layout_constraintStart_toEndOf="@id/back_button"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintBottom_toBottomOf="parent" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <!-- Group ID Input -->
                        <EditText
                            android:id="@+id/course_id_input"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            android:background="@drawable/edit_text_border"
                            android:hint="Group ID"
                            android:padding="16dp"
                            android:textColor="#0D0302"
                            android:textColorHint="#808080"
                            android:textSize="22sp"
                            android:inputType="text"
                            android:autofillHints="username" />

                        <!-- Group Name Input -->
                        <EditText
                            android:id="@+id/group_name_input"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            android:background="@drawable/edit_text_border"
                            android:hint="Group Name"
                            android:padding="16dp"
                            android:textColor="#0D0302"
                            android:textColorHint="#808080"
                            android:textSize="22sp"
                            android:inputType="text"
                            android:autofillHints="name" />

                        <!-- Group Description Input -->
                        <EditText
                            android:id="@+id/group_description_input"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            android:background="@drawable/edit_text_border"
                            android:hint="Group Description"
                            android:padding="16dp"
                            android:textColor="#0D0302"
                            android:textColorHint="#808080"
                            android:textSize="20sp"
                            android:inputType="textMultiLine"
                            android:minLines="1"
                            android:autofillHints="" />

                        <!-- Select Participants Button -->
                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Select Participants"
                            android:textColor="#006BFF"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:layout_marginTop="8dp"
                            android:layout_marginBottom="8dp"/>

                        <LinearLayout
                            android:id="@+id/select_participants_button"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:clickable="true"
                            android:focusable="true"
                            android:background="@drawable/edit_text_border"
                            android:padding="12dp">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="Search and select users"
                                android:textColor="#0D0302"
                                android:textSize="18sp" />

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:src="@drawable/ic_search"
                                android:contentDescription="Search users" />
                        </LinearLayout>

                        <!-- Search Box (Initially Hidden) -->
                        <LinearLayout
                            android:id="@+id/search_participants_container"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:background="@drawable/edit_text_border"
                            android:layout_marginTop="8dp"
                            android:padding="8dp"
                            android:visibility="visible">
                            
                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:src="@drawable/ic_search"
                                android:layout_marginEnd="8dp"
                                android:layout_gravity="center_vertical" />
                                
                            <EditText
                                android:id="@+id/search_participants_input"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:hint="Search users..."
                                android:textSize="16sp"
                                android:background="@null"
                                android:inputType="text"
                                android:imeOptions="actionSearch" />
                                
                            <ImageButton
                                android:id="@+id/clear_search_button"
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:src="@android:drawable/ic_menu_close_clear_cancel"
                                android:background="?attr/selectableItemBackgroundBorderless"
                                android:visibility="gone" />
                        </LinearLayout>

                        <!-- Title for participant list -->
                        <TextView
                            android:id="@+id/selected_participants_title"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Selected Participants: (1)"
                            android:textColor="#006BFF"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:layout_marginTop="12dp"
                            android:layout_marginBottom="4dp"
                            android:visibility="visible" />

                        <!-- Participant list -->
                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/participants_recycler_view"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:minHeight="200dp"
                            android:layout_marginTop="8dp"
                            android:layout_marginBottom="8dp"
                            android:importantForAccessibility="yes"
                            android:contentDescription="@string/participants_list_description"
                            android:visibility="visible"
                            tools:listitem="@layout/item_participant" />

                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Empty space to push content up from button -->
            <View
                android:layout_width="match_parent"
                android:layout_height="16dp" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <!-- Proceed Button - now outside NestedScrollView -->
    <Button
        android:id="@+id/create_group_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="24dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/button_primary"
        android:elevation="4dp"
        android:padding="16dp"
        android:text="Proceed"
        android:textColor="#FFFFFF"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout> 