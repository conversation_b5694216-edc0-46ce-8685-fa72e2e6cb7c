<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">ChatApp</string>
    <string name="feature1">Feature1</string>
    <string name="feature2">Feature2</string>
    <string name="feature3">Feature3</string>
    <string name="chatting">Chatting</string>
    <string name="feature4">Feature4</string>
    <string name="whatsapp">WhatsApp</string>
    <string name="chats">Chats</string>
    <string name="groups">Groups</string>
    <string name="community">Community</string>
    <string name="search">Search</string>
    <string name="more">More</string>
    <string name="message_admin">Message Admin</string>
    <string name="no_admins_found">No Admin1 members found</string>
    <string name="new_chat">New Chat</string>
    <string name="new_group">New Group</string>
    <string name="no_chats_available">No chats available</string>
    <string name="no_groups_available">No groups available</string>
    <string name="no_communities_available">No communities available</string>
    <string name="email">Email</string>
    <string name="password">Password</string>
    <string name="login">Login</string>
    <string name="submit">Submit</string>
    <string name="admin1">Admin1</string>
    <string name="admin2">Admin2</string>
    <string name="student">Student</string>
    <string name="invalid_email">Invalid Email Address</string>
    <string name="invalid_password">Password must be at least 6 characters</string>
    <string name="account_created">Account Created Successfully</string>
    <string name="login_success">Login Successful</string>
    <string name="login_failed">Login Failed</string>
    <string name="error_occurred">An error occurred</string>
    <string name="invalid_account">Invalid account type or credentials</string>
    <string name="logout">Logout</string>
    <string name="profile">Profile</string>
    <string name="notification">Notification</string>
    <string name="settings">Settings</string>
    <string name="home">Home</string>
    <string name="signup">Sign Up</string>
    <string name="already_have_account">Already have an account? Login</string>
    <string name="back">Back</string>
    <string name="create_group">Create Group</string>
    <string name="group_name">Group Name</string>
    <string name="group_description">Group Description</string>
    <string name="create">Create</string>
    <string name="add_group">Add Group</string>
    <string name="remove_group">Remove Group</string>
    <string name="confirm_group_removal">Are you sure you want to remove this group?</string>
    <string name="yes">Yes</string>
    <string name="no">No</string>
    <string name="group_deleted">Group deleted successfully</string>
    <string name="group_deletion_failed">Failed to delete group</string>
    <string name="no_groups_found">No groups found</string>
    <string name="send_message">Send Message</string>
    <string name="type_message">Type a message</string>
    <string name="message_sent">Message sent</string>
    <string name="message_failed">Failed to send message</string>
    <string name="group_chat">Group Chat</string>
    
    <!-- Add Group Fragment -->
    <string name="back_navigation">Back</string>
    <string name="group_id_hint">Group ID</string>
    <string name="group_name_hint">Group Name</string>
    <string name="group_description_hint">Group Description</string>
    <string name="select_participants">Select Participants</string>
    <string name="plus_symbol">+</string>
    <string name="selected_participants_default">Selected Participants (0)</string>
    <string name="selected_participants_count">Selected Participants (%1$d)</string>
    <string name="participants_list_description">List of participants to select for the group</string>
    <string name="create_group_button">CREATE GROUP</string>
    
    <!-- Login screen -->
    <string name="log_in">Log In</string>
    <string name="log_in_first_time">Log In First Time</string>
    <string name="teaching_and_technical">Teaching And Technical</string>
    <string name="wing">Wing</string>
    <string name="empowering_minds">Empowering Minds, Enriching Lives</string>
    <string name="bridging_knowledge">Bridging Knowledge with Service</string>
    <string name="background_shapes">Background Shapes</string>
    <string name="book_icon">Book Icon</string>
    <string name="roll_number">Roll Number</string>
    <string name="passkey">Passkey</string>
    <string name="sign_in">Sign In</string>
    <string name="greeting_morning">Good Morning</string>
    <string name="greeting_afternoon">Good Afternoon</string>
    <string name="greeting_evening">Good Evening</string>
    <string name="next_class">NEXT CLASS</string>
    <string name="tasks">Tasks</string>
    <string name="chat">Chat</string>
    <string name="updates">Updates</string>
    <string name="no_updates">No updates available</string>
    <string name="no_updates_available">No updates available</string>
    <string name="add_update">Add Update</string>
    <string name="location">Location</string>
    <string name="time">Time</string>
    <string name="welcome">Welcome</string>
    <string name="hello_blank_fragment">Hello blank fragment</string>
    <string name="add_file">Add File Button</string>
    <string name="item_icon">Item Icon</string>
</resources>