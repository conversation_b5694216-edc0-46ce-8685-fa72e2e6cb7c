<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".fragments.LibrarySubCategoriesFragment">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewLibrarySubCategories"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Checkbox to toggle between creating a subcategory/level/etc. and uploading a file -->
    <CheckBox
        android:id="@+id/checkboxCreateSection"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Create Section"
        android:textColor="@android:color/white"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="8dp"
        app:layout_constraintBottom_toTopOf="@+id/fabAddSubCategory"
        app:layout_constraintEnd_toEndOf="parent"
        android:visibility="gone" /> <!-- Initially hidden -->

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabAddSubCategory"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:clickable="true"
        android:focusable="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:srcCompat="@android:drawable/ic_input_add"
        android:visibility="gone"/> <!-- Initially hidden -->

</androidx.constraintlayout.widget.ConstraintLayout> 