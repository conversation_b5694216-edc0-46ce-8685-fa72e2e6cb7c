package com.phad.chatapp.ui.profile

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import android.content.Intent
import com.phad.chatapp.utils.SessionManager

class NssOrTeachingWingActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val teachingWing = intent.getBooleanExtra("teaching_wing", false)
        val sessionManager = SessionManager(this)
        setContent {
            NssOrTeachingWingScreen(
                teachingWing = teachingWing,
                onNssClick = {
                    sessionManager.setLastInterfaceChoice("NSS")
                    val intent = Intent(this, com.phad.chatapp.NssMainActivity::class.java)
                    intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                    startActivity(intent)
                    finish()
                },
                onTeachingWingClick = {
                    sessionManager.setLastInterfaceChoice("TEACHING_WING")
                    val intent = Intent(this, com.phad.chatapp.MainActivity::class.java)
                    intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                    startActivity(intent)
                    finish()
                }
            )
        }
    }
} 