package com.phad.chatapp.fragments

import android.Manifest
import android.app.AlertDialog
import android.content.ContentValues
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.location.Location
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.provider.MediaStore
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.google.android.gms.location.FusedLocationProviderClient
import com.google.android.gms.location.LocationServices
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.android.material.snackbar.Snackbar
import com.google.firebase.Timestamp
import com.google.firebase.firestore.GeoPoint
import com.google.firebase.firestore.ktx.firestore
import com.google.firebase.ktx.Firebase
import com.phad.chatapp.R
import com.phad.chatapp.adapters.LiveEventsAdapter
import com.phad.chatapp.databinding.DialogCreateEventBinding
import com.phad.chatapp.databinding.DialogPendingAttendanceBinding
import com.phad.chatapp.databinding.FragmentAttendanceManagerBinding
import com.phad.chatapp.databinding.ItemLiveEventBinding
import com.phad.chatapp.databinding.ItemPendingAttendanceBinding
import com.phad.chatapp.models.AttendanceEvent
import com.phad.chatapp.models.AttendanceSubmission
import com.phad.chatapp.models.User
import com.phad.chatapp.utils.CloudinaryManager
import com.phad.chatapp.utils.PermissionManager
import com.phad.chatapp.viewmodels.AttendanceViewModel
import com.phad.chatapp.viewmodels.AttendanceViewModelFactory
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import androidx.navigation.fragment.findNavController
import com.phad.chatapp.utils.AttendanceUtils
import com.phad.chatapp.utils.AttendanceStatsUpdater

class NssAttendanceManagerFragment : Fragment() {
    // ... (copy the entire AttendanceManagerFragment class body here, but update the class name)
} 