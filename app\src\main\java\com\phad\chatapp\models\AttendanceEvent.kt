package com.phad.chatapp.models

import com.google.firebase.Timestamp
import com.google.firebase.firestore.DocumentId

/**
 * Model class representing an attendance event in the system
 */
data class AttendanceEvent(
    @DocumentId
    val id: String = "", // Format: "{eventName}_{timestamp}"
    val name: String = "",
    val description: String = "",
    val createdBy: String = "", // Roll number of the admin who created the event
    val createdAt: Timestamp = Timestamp.now(),
    val totalMarked: Int = 0,
    val eventStatus: String = "Live" // "Live" or "End"
) {
    companion object {
        // Status constants
        const val STATUS_LIVE = "Live"
        const val STATUS_END = "End"
    }
    
    /**
     * Check if event is live
     */
    fun isLive(): Boolean {
        return eventStatus == STATUS_LIVE
    }
    
    /**
     * Check if event is ended
     */
    fun isEnded(): Boolean {
        return eventStatus == STATUS_END
    }
    
    /**
     * Format the timestamp as a string
     */
    fun getFormattedTimestamp(): String {
        val date = createdAt.toDate()
        val formatter = java.text.SimpleDateFormat("dd MMM yyyy, HH:mm:ss", java.util.Locale.getDefault())
        return formatter.format(date)
    }
    
    /**
     * Parse eventName and timestamp from ID
     */
    fun parseIdComponents(): Pair<String, String> {
        val components = id.split("_", limit = 2)
        return if (components.size == 2) {
            Pair(components[0], components[1])
        } else {
            Pair(id, "")
        }
    }
}
