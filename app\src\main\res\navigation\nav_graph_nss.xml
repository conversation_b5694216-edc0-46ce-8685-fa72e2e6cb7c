<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_graph_nss"
    app:startDestination="@id/nssHomeFragment">

    <fragment
        android:id="@+id/nssHomeFragment"
        android:name="com.phad.chatapp.fragments.NssHomeFragment"
        android:label="NSS Home"
        tools:layout="@layout/fragment_home" />

    <fragment
        android:id="@+id/nssCalendarFragment"
        android:name="com.phad.chatapp.features.calendar.ui.NssCalendarFragment"
        android:label="NSS Calendar"
        tools:layout="@layout/fragment_calendar" />

    <fragment
        android:id="@+id/nssProfileFragment"
        android:name="com.phad.chatapp.fragments.NssProfileFragment"
        android:label="NSS Profile"
        tools:layout="@layout/fragment_profile" />

    <fragment
        android:id="@+id/nssAttendanceManagerFragment"
        android:name="com.phad.chatapp.fragments.NssAttendanceManagerFragment"
        android:label="NSS Attendance Manager"
        tools:layout="@layout/fragment_attendance_manager" />

    <fragment
        android:id="@+id/nssAdminAttendanceApprovalFragment"
        android:name="com.phad.chatapp.fragments.NssAdminAttendanceApprovalFragment"
        android:label="NSS Attendance Approval"
        tools:layout="@layout/fragment_admin_attendance_approval" />

    <!-- Add other NSS-specific fragments as needed -->

</navigation> 