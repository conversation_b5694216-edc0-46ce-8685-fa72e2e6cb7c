<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <TextView
            android:id="@+id/rollNumberTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@android:color/black"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="Roll Number: 12345" />

        <TextView
            android:id="@+id/timestampTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:textColor="#757575"
            android:layout_marginTop="4dp"
            app:layout_constraintTop_toBottomOf="@id/rollNumberTextView"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="Submitted: 2025-05-24 10:15:30" />

        <ImageView
            android:id="@+id/attendanceImageView"
            android:layout_width="0dp"
            android:layout_height="200dp"
            android:layout_marginTop="8dp"
            android:scaleType="centerCrop"
            android:background="#EEEEEE"
            app:layout_constraintTop_toBottomOf="@id/timestampTextView"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:contentDescription="Attendance Image" />

        <Button
            android:id="@+id/approveButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="Approve"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="4dp"
            app:layout_constraintTop_toBottomOf="@id/attendanceImageView"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/rejectButton" />

        <Button
            android:id="@+id/rejectButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="Reject"
            android:layout_marginTop="8dp"
            android:layout_marginStart="4dp"
            app:layout_constraintTop_toBottomOf="@id/attendanceImageView"
            app:layout_constraintStart_toEndOf="@id/approveButton"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView> 