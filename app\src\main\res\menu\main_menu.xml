<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <item
        android:id="@+id/action_search"
        android:icon="@drawable/ic_search"
        android:title="Search"
        app:showAsAction="always" />

    <item
        android:id="@+id/action_more"
        android:icon="@drawable/ic_more_vert"
        android:title="More"
        app:showAsAction="always">

        <menu>
            <group android:id="@+id/admin_group">
                <item
                    android:id="@+id/action_create_group"
                    android:icon="@drawable/ic_add_group"
                    android:title="Create New Group"
                    app:showAsAction="never" />

                <item
                    android:id="@+id/action_remove_group"
                    android:icon="@drawable/ic_remove_group"
                    android:title="Remove Group"
                    app:showAsAction="never" />
            </group>

            <item
                android:id="@+id/action_logout"
                android:icon="@drawable/ic_logout"
                android:title="Logout"
                app:showAsAction="never" />

            <!-- TWApp Student Data and Test Firebase options will be added programmatically -->
        </menu>
    </item>
</menu> 