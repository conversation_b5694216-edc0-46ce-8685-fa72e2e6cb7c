<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#0d0302"
    tools:context=".fragments.LibraryFilesFragment">

    <!-- Top section with background images -->
    <FrameLayout
        android:id="@+id/top_image_container"
        android:layout_width="0dp"
        android:layout_height="450dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- Base background image -->
        <ImageView
            android:id="@+id/background_image_base"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitXY"
            android:src="@drawable/vector271"
            android:contentDescription="Background image" />

        <!-- Overlaid rounded image with padding -->
        <ImageView
            android:id="@+id/background_image_overlay"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="50dp"
            android:layout_marginEnd="50dp"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="100dp"
            android:scaleType="fitXY"
            android:src="@drawable/vector272"
            android:contentDescription="Overlay image" />

    </FrameLayout>

    <!-- ScrollView for content, starting from the top -->
    <ScrollView
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerViewLibraryItemList"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:color/transparent" />

    </ScrollView>

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabAddItem"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:clickable="true"
        android:focusable="true"
        android:backgroundTint="#FFCC00"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:srcCompat="@drawable/ic_add"
        android:contentDescription="@string/add_file"
        app:tint="@android:color/black" />

</androidx.constraintlayout.widget.ConstraintLayout>