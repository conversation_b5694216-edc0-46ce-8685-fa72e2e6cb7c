<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="16dp">

    <TextView
        android:id="@+id/dialogTitleTextView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="Attendance Details"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@android:color/black"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- User info section -->
    <TextView
        android:id="@+id/nameLabel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Name:"
        android:textSize="14sp"
        android:textStyle="bold"
        android:textColor="@android:color/black"
        android:layout_marginTop="16dp"
        app:layout_constraintTop_toBottomOf="@id/dialogTitleTextView"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/nameTextView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textSize="14sp"
        android:textColor="@android:color/black"
        android:layout_marginStart="8dp"
        app:layout_constraintTop_toTopOf="@id/nameLabel"
        app:layout_constraintStart_toEndOf="@id/nameLabel"
        app:layout_constraintEnd_toEndOf="parent"
        tools:text="Rabi Kumar Shaw" />

    <TextView
        android:id="@+id/rollNumberLabel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Roll Number:"
        android:textSize="14sp"
        android:textStyle="bold"
        android:textColor="@android:color/black"
        android:layout_marginTop="8dp"
        app:layout_constraintTop_toBottomOf="@id/nameTextView"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/rollNumberTextView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textSize="14sp"
        android:textColor="@android:color/black"
        android:layout_marginStart="8dp"
        app:layout_constraintTop_toTopOf="@id/rollNumberLabel"
        app:layout_constraintStart_toEndOf="@id/rollNumberLabel"
        app:layout_constraintEnd_toEndOf="parent"
        tools:text="2201CE49" />

    <TextView
        android:id="@+id/timestampLabel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Timestamp:"
        android:textSize="14sp"
        android:textStyle="bold"
        android:textColor="@android:color/black"
        android:layout_marginTop="8dp"
        app:layout_constraintTop_toBottomOf="@id/rollNumberTextView"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/timestampTextView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textSize="14sp"
        android:textColor="@android:color/black"
        android:layout_marginStart="8dp"
        app:layout_constraintTop_toTopOf="@id/timestampLabel"
        app:layout_constraintStart_toEndOf="@id/timestampLabel"
        app:layout_constraintEnd_toEndOf="parent"
        tools:text="19 May 2025, 21:31:02" />

    <TextView
        android:id="@+id/locationLabel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Location:"
        android:textSize="14sp"
        android:textStyle="bold"
        android:textColor="@android:color/black"
        android:layout_marginTop="8dp"
        app:layout_constraintTop_toBottomOf="@id/timestampTextView"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/locationTextView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textSize="14sp"
        android:textColor="@android:color/black"
        android:layout_marginStart="8dp"
        app:layout_constraintTop_toTopOf="@id/locationLabel"
        app:layout_constraintStart_toEndOf="@id/locationLabel"
        app:layout_constraintEnd_toEndOf="parent"
        tools:text="Lat: 28.6139, Long: 77.2090" />

    <!-- Images section -->
    <TextView
        android:id="@+id/imagesLabel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Face Comparison:"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@android:color/black"
        android:layout_marginTop="16dp"
        app:layout_constraintTop_toBottomOf="@id/locationTextView"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/submittedImageLabel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Submitted"
        android:textSize="14sp"
        android:textColor="@android:color/black"
        android:layout_marginTop="8dp"
        app:layout_constraintTop_toBottomOf="@id/imagesLabel"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/referenceImageLabel" />

    <TextView
        android:id="@+id/referenceImageLabel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Reference"
        android:textSize="14sp"
        android:textColor="@android:color/black"
        android:layout_marginTop="8dp"
        app:layout_constraintTop_toBottomOf="@id/imagesLabel"
        app:layout_constraintStart_toEndOf="@id/submittedImageLabel"
        app:layout_constraintEnd_toEndOf="parent" />

    <ImageView
        android:id="@+id/submittedImageView"
        android:layout_width="140dp"
        android:layout_height="140dp"
        android:layout_marginTop="8dp"
        android:scaleType="centerCrop"
        android:background="#EEEEEE"
        app:layout_constraintTop_toBottomOf="@id/submittedImageLabel"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/referenceImageView"
        android:contentDescription="Submitted Image" />

    <ImageView
        android:id="@+id/referenceImageView"
        android:layout_width="140dp"
        android:layout_height="140dp"
        android:layout_marginTop="8dp"
        android:scaleType="centerCrop"
        android:background="#EEEEEE"
        app:layout_constraintTop_toBottomOf="@id/referenceImageLabel"
        app:layout_constraintStart_toEndOf="@id/submittedImageView"
        app:layout_constraintEnd_toEndOf="parent"
        android:contentDescription="Reference Image" />

    <!-- Action buttons -->
    <Button
        android:id="@+id/approveButton"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="Approve"
        android:layout_marginTop="24dp"
        android:layout_marginEnd="8dp"
        app:layout_constraintTop_toBottomOf="@id/submittedImageView"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/rejectButton" />

    <Button
        android:id="@+id/rejectButton"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="Reject"
        android:layout_marginTop="24dp"
        android:layout_marginStart="8dp"
        app:layout_constraintTop_toBottomOf="@id/referenceImageView"
        app:layout_constraintStart_toEndOf="@id/approveButton"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout> 