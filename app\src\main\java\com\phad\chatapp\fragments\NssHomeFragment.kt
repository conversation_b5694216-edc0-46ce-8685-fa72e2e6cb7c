package com.phad.chatapp.fragments

import android.app.Activity
import android.app.Dialog
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.widget.Button
import android.widget.EditText
import android.widget.FrameLayout
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.phad.chatapp.ChatActivity
import com.phad.chatapp.MainActivity
import com.phad.chatapp.R
import com.phad.chatapp.utils.DriveServiceHelper
import com.phad.chatapp.utils.SessionManager
import com.phad.chatapp.adapters.UpdateCardAdapter
import com.phad.chatapp.models.Update
import java.io.FileNotFoundException
import java.util.Calendar
import java.util.UUID
import com.google.firebase.firestore.FieldValue
import com.phad.chatapp.utils.NotificationHelper
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import androidx.navigation.fragment.findNavController
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.updatePadding
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.platform.ComposeView
import androidx.lifecycle.lifecycleScope
import com.phad.chatapp.ui.home.HomeScreen
import com.phad.chatapp.ui.home.HomeUiState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update

class NssHomeFragment : Fragment() {
    private val TAG = "NssHomeFragment"
    
    private lateinit var sessionManager: SessionManager
    private val db = FirebaseFirestore.getInstance()
    private val auth = FirebaseAuth.getInstance()
    private lateinit var driveServiceHelper: DriveServiceHelper
    
    // Create update dialog
    private var createUpdateDialog: Dialog? = null
    private var selectedImageUri: Uri? = null
    private var selectedDocumentUri: Uri? = null
    
    // Image picker launcher
    private val imagePicker = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.data?.let { uri ->
                selectedImageUri = uri
                showSelectedImage(uri)
            }
        }
    }
    
    // Document picker launcher
    private val documentPicker = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.data?.let { uri ->
                selectedDocumentUri = uri
                showSelectedDocument(uri)
            }
        }
    }
    
    private val _uiState = MutableStateFlow(HomeUiState())
    private val uiState: StateFlow<HomeUiState> = _uiState.asStateFlow()
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply { 
            setContent {
                val state by uiState.collectAsState()
                HomeScreen(
                    state = state,
                    onChatbotClick = {
                        val intent = Intent(requireContext(), com.phad.chatapp.features.home.chatbot.ui.ChatBotActivity::class.java)
                        startActivity(intent)
                    },
                    onTodoClick = { navigateToAttendanceManager() },
                    onAddUpdateClick = { showCreateUpdateDialog() },
                    onUpdateClick = { update ->
                        val intent = Intent(requireContext(), com.phad.chatapp.activities.UpdateDetailActivity::class.java)
                        intent.putExtra(com.phad.chatapp.activities.UpdateDetailActivity.EXTRA_UPDATE, update)
                        startActivity(intent)
                    }
                )
            }
        }
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        sessionManager = SessionManager(requireContext())
        driveServiceHelper = DriveServiceHelper.getInstance(requireContext())
        
        loadGreetingAndNextClass()
        loadUpdates()
    }
    
    private fun loadGreetingAndNextClass() {
        val greeting = getGreetingBasedOnTime()
        val userName = sessionManager.fetchUserName().ifEmpty { "User" }
        val nextClassInfo = "24 April, 3:00 PM in Raghunpur" // Static for now

        _uiState.update {
            it.copy(
                greeting = greeting,
                userName = userName,
                nextClassInfo = nextClassInfo,
                isAdmin = sessionManager.fetchUserType() == "Admin1" || sessionManager.fetchUserType() == "Admin2"
            )
        }
    }
    
    private fun getGreetingBasedOnTime(): String {
        val calendar = Calendar.getInstance()
        return when (calendar.get(Calendar.HOUR_OF_DAY)) {
            in 0..11 -> "Good Morning"
            in 12..16 -> "Good Afternoon"
            else -> "Good Evening"
        }
    }
    
    private fun loadUpdates() {
        db.collection("nss_updates")
            .orderBy("timestamp", Query.Direction.DESCENDING)
            .limit(10)
            .get()
            .addOnSuccessListener { documents ->
                val updates = documents.toObjects(Update::class.java).map { update ->
                    var tempUpdate = update
                    val imageUrl = tempUpdate.mediaUrl ?: tempUpdate.imageUrl
                    if (!imageUrl.isNullOrEmpty()) {
                        tempUpdate = tempUpdate.copy(imageUrl = driveServiceHelper.processGoogleDriveUrl(imageUrl))
                    }
                    tempUpdate
                }
                _uiState.update { it.copy(updates = updates) }
            }
            .addOnFailureListener { e ->
                Log.e(TAG, "Error loading updates", e)
                Toast.makeText(context, "Failed to load updates.", Toast.LENGTH_SHORT).show()
            }
    }

    private fun showSelectedImage(uri: Uri) {
        val intent = Intent(Intent.ACTION_VIEW, uri)
        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        startActivity(intent)
    }

    private fun showSelectedDocument(uri: Uri) {
        val intent = Intent(Intent.ACTION_VIEW, uri)
        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        startActivity(intent)
    }

    private fun navigateToAttendanceManager() {
        val intent = Intent(requireContext(), com.phad.chatapp.fragments.NssAttendanceManagerFragment::class.java)
        startActivity(intent)
    }

    private fun showCreateUpdateDialog() {
        val dialogView = LayoutInflater.from(requireContext()).inflate(R.layout.dialog_create_update, null)
        val contentEditText = dialogView.findViewById<EditText>(R.id.updateContentInput)
        val titleEditText = dialogView.findViewById<EditText>(R.id.updateTitleInput)
        createUpdateDialog = MaterialAlertDialogBuilder(requireContext())
            .setTitle("Create New Update")
            .setMessage("What's on your mind?")
            .setView(dialogView)
            .setPositiveButton("Post") { _, _ ->
                val title = titleEditText.text?.toString()
                val description = contentEditText.text?.toString()
                val mediaUrl = selectedImageUri?.toString() ?: selectedDocumentUri?.toString()

                if ((title?.isNotEmpty() == true) || (mediaUrl?.isNotEmpty() == true) || (description?.isNotEmpty() == true)) {
                    val update = Update(
                        id = UUID.randomUUID().toString(),
                        authorId = auth.currentUser?.uid ?: "",
                        authorName = sessionManager.fetchUserName(),
                        authorImageUrl = auth.currentUser?.photoUrl?.toString(),
                        title = if (title?.isNotEmpty() == true) title else null,
                        content = if (description?.isNotEmpty() == true) description else title ?: "",
                        mediaUrl = if (mediaUrl?.isNotEmpty() == true) mediaUrl else null,
                        imageUrl = null,
                        documentUrl = null,
                        documentName = null,
                        qrCodeUrl = null,
                        url = null,
                        externalLink = null,
                        timestamp = System.currentTimeMillis(),
                        targetGroups = listOf(),
                        category = "general",
                        locationName = null,
                        locationAddress = null,
                        locationLatitude = null,
                        locationLongitude = null,
                        eventDate = null,
                        eventEndDate = null,
                        isImportant = false,
                        tags = listOf()
                    )

                    db.collection("nss_updates")
                        .add(update)
                        .addOnSuccessListener {
                            Toast.makeText(requireContext(), "Update posted!", Toast.LENGTH_SHORT).show()
                            createUpdateDialog?.dismiss()
                            loadUpdates() // Reload updates to show the new one
                        }
                        .addOnFailureListener { e ->
                            Log.e(TAG, "Error posting update", e)
                            Toast.makeText(requireContext(), "Failed to post update.", Toast.LENGTH_SHORT).show()
                        }
                } else {
                    Toast.makeText(requireContext(), "Please add a title, description, or media.", Toast.LENGTH_SHORT).show()
                }
                selectedImageUri = null
                selectedDocumentUri = null
            }
            .setNegativeButton("Cancel") { _, _ ->
                createUpdateDialog?.dismiss()
                selectedImageUri = null
                selectedDocumentUri = null
            }
            .show()
    }
} 