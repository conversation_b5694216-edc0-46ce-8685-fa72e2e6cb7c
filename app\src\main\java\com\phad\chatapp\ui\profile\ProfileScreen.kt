package com.phad.chatapp.ui.profile

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.phad.chatapp.R

data class ProfileUiState(
    val name: String = "Loading...",
    val location: String = "...",
    val email: String = "loading...",
    val phone: String = "loading...",
    val rollNumber: String = "loading...",
    val collegeEmail: String = "loading...",
    val academicGroup: String = "loading...",
    val nssGroup: String = "loading...",
    val topic1: String = "loading...",
    val topic2: String = "loading...",
    val topic3: String = "loading...",
    val userType: String = "Student",
    val events: String = "-/-",
    val classes: String = "-/-",
    val meetings: String = "-/-",
    val isStudent: Boolean = true,
    val Teaching_wing: Boolean = false
)

@Composable
fun ProfileScreen(
    modifier: Modifier = Modifier,
    state: ProfileUiState,
    onLogoutClick: () -> Unit,
    onChatbotClick: () -> Unit,
    onLibraryClick: () -> Unit,
    onChatClick: () -> Unit,
    onScheduleClick: () -> Unit,
    onSwitchInterfaceClick: () -> Unit,
    currentInterface: String
) {
    val isDarkTheme = isSystemInDarkTheme()
    val backgroundColor = Color(0xff0d0302)
    val surfaceColor =Color.White
    val onSurfaceColor = Color.Black
    val onSurfaceVariantColor = onSurfaceColor.copy(alpha = 0.6f)
    val onBackgroundColor =  Color.White

    Surface(
        modifier = modifier.fillMaxSize(),
        color = backgroundColor
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
        ) {
            // Top Section with background images and stats
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(450.dp) // Adjusted height for responsiveness
            ) {
                Image(
                    painter = painterResource(id = R.drawable.vector271),
                    contentDescription = null,
                    contentScale = ContentScale.FillBounds,
                    modifier = Modifier.fillMaxSize()
                )
                Image(
                    painter = painterResource(id = R.drawable.vector272),
                    contentDescription = null,
                    contentScale = ContentScale.FillBounds,
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(start = 50.dp, end = 50.dp, top = 20.dp, bottom = 100.dp)
                        .clip(RoundedCornerShape(150.dp))
                )

                // Header with logo and icons
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 24.dp, vertical = 24.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.thelogosmall),
                        contentDescription = "The Logo Small",
                        colorFilter = ColorFilter.tint(Color(0xffffcc00)),
                        modifier = Modifier.size(width = 72.dp, height = 40.dp)
                    )
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        // Add Switch Interface icon for Teaching_wing admins
                        if (!state.isStudent && state.Teaching_wing) {
                            val switchTargetText = if (currentInterface == "Teaching Wing") "NSS" else "Teaching Wing"
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally,
                                modifier = Modifier
                                    .clickable { onSwitchInterfaceClick() }
                                    .padding(horizontal = 4.dp)
                            ) {
                                Image(
                                    painter = painterResource(id = R.drawable.switch_account),
                                    contentDescription = "Switch to $switchTargetText",
                                    colorFilter = ColorFilter.tint(onBackgroundColor),
                                    modifier = Modifier.size(30.dp)
                                )
                            }
                            Spacer(modifier = Modifier.size(12.dp))
                        }

                        Image(
                            painter = painterResource(id = R.drawable.ic_library),
                            contentDescription = "Library Icon",
                            colorFilter = ColorFilter.tint(onBackgroundColor),
                            modifier = Modifier
                                .size(30.dp)
                                .clickable { onLibraryClick() }
                        )
                        Spacer(modifier = Modifier.size(16.dp))
                        Image(
                            painter = painterResource(id = R.drawable.ic_robot),
                            contentDescription = "Chatbot Icon",
                            colorFilter = ColorFilter.tint(onBackgroundColor),
                            modifier = Modifier
                                .size(30.dp)
                                .clickable { onChatbotClick() }
                        )
                    }
                }

                // Stats Section
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .align(Alignment.BottomCenter)
                        .padding(bottom = 80.dp), // Pushed up to overlap the card
                    horizontalArrangement = Arrangement.SpaceEvenly,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    StatItem(label = "Events", value = state.events, color = onBackgroundColor)
                    StatItem(label = "Classes", value = state.classes, size = 48.sp, color = onBackgroundColor)
                    StatItem(label = "Meetings", value = state.meetings, color = onBackgroundColor)
                }
            }

            // Bottom Section with profile details
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(topStart = 40.dp, topEnd = 40.dp))
                    .background(surfaceColor)
                    .padding(24.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = state.name,
                            color = onSurfaceColor,
                            fontSize = 24.sp,
                            fontWeight = FontWeight.Bold
                        )
                    }
                    Column(
                        modifier = Modifier.weight(1f),
                        horizontalAlignment = Alignment.End
                    ) {
                        Text(text = state.email, color = onSurfaceVariantColor, fontSize = 12.sp)
                        Text(text = state.phone, color = onSurfaceVariantColor, fontSize = 12.sp)
                    }
                }

                Spacer(modifier = Modifier.height(24.dp))

                ProfileDetailItem(label = state.rollNumber, value = state.collegeEmail, color = onSurfaceColor)
                
                if (state.isStudent) {
                    ProfileDetailItem(label = "", value = state.academicGroup, color = onSurfaceColor)
                    ProfileDetailItem(label = "", value = state.nssGroup, color = onSurfaceColor)
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    Text(
                        text = "Topics of Interest",
                        color = onSurfaceVariantColor,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold
                    )
                    ProfileDetailItem(label = "", value = state.topic1, color = onSurfaceColor)
                    ProfileDetailItem(label = "", value = state.topic2, color = onSurfaceColor)
                    ProfileDetailItem(label = "", value = state.topic3, color = onSurfaceColor)
                } else { // Admin
                    ProfileDetailItem(label = "", value = state.academicGroup, color = onSurfaceColor) // Committee
                    
                    Spacer(modifier = Modifier.height(16.dp))

                    Text(
                        text = "Course Codes",
                        color = onSurfaceVariantColor,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold
                    )
                    ProfileDetailItem(label = "", value = state.topic1, color = onSurfaceColor) // Course Code
                }
                
                Spacer(modifier = Modifier.height(32.dp))

                // The button is now removed from here

                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clip(RoundedCornerShape(12.dp))
                        .background(onSurfaceVariantColor.copy(alpha = 0.1f))
                        .clickable { onLogoutClick() }
                        .padding(vertical = 16.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Center
                    ) {
                        Image(
                            painter = painterResource(id = R.drawable.logout),
                            contentDescription = "Logout Icon",
                            modifier = Modifier.size(24.dp)
                        )
                        Spacer(modifier = Modifier.size(8.dp))
                        Text(
                            text = "Log Out",
                            color = Color.Red,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold,
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }

            // Add a spacer at the bottom to push content above the nav bar
            Spacer(modifier = Modifier.height(80.dp))
        }
    }
}

@Composable
fun StatItem(label: String, value: String, size: androidx.compose.ui.unit.TextUnit = 32.sp, color: Color) {
    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        Text(text = value, color = color, fontSize = size, fontWeight = FontWeight.Bold)
        Text(text = label, color = color.copy(alpha = 0.7f), fontSize = 12.sp)
    }
}

@Composable
fun ProfileDetailItem(label: String, value: String, color: Color) {
    Column(modifier = Modifier.padding(vertical = 4.dp)) {
        if (label.isNotEmpty()) {
            Text(
                text = label,
                color = color,
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold
            )
        }
        Text(
            text = value,
            color = color.copy(alpha = 0.7f),
            fontSize = 14.sp,
            fontWeight = FontWeight.SemiBold
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun ProfilePreview() {
    MaterialTheme {
        ProfileScreen(
            state = ProfileUiState(name = "Tashya Aryan", location = "N/A"),
            onLogoutClick = {},
            onChatbotClick = {},
            onLibraryClick = {},
            onChatClick = {},
            onScheduleClick = {},
            onSwitchInterfaceClick = {},
            currentInterface = "Teaching Wing"
        )
    }
} 